"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  FileText,
  Code,
  Lightbulb,
  ExternalLink,
  Copy,
  Check,
  ChevronDown,
  ChevronRight,
  Menu
} from "lucide-react";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AgentDocsPage() {
  const [activeTab, setActiveTab] = useState("introduction");
  const [activeSubTab, setActiveSubTab] = useState<string | null>(null);
  const [activeLang, setActiveLang] = useState("typescript");
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Function to handle language change across all tabs
  const handleLangChange = (lang: string) => {
    setActiveLang(lang);
  };

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopiedId(id);
    toast.success("Code copied to clipboard");
    setTimeout(() => setCopiedId(null), 2000);
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="border-b">
        <div className="container max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/agents">
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                    <path d="m12 19-7-7 7-7"/>
                    <path d="M19 12H5"/>
                  </svg>
                </Button>
              </Link>
              <h1 className="text-2xl font-bold">AdMesh Documentation</h1>
            </div>
            <div className="flex space-x-2">
              <Button asChild variant="outline">
                <Link href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" className="flex items-center">
                  <Code className="mr-2 h-4 w-4" /> TypeScript SDK
                  <ExternalLink className="ml-1 h-3 w-3" />
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" className="flex items-center">
                  <Code className="mr-2 h-4 w-4" /> Python SDK
                  <ExternalLink className="ml-1 h-3 w-3" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Button */}
      <div className="md:hidden sticky top-0 z-10 bg-background border-b p-4">
        <Button
          variant="outline"
          className="w-full flex items-center justify-between"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          <span className="flex items-center">
            <Menu className="mr-2 h-4 w-4" />
            {activeTab === "introduction" && "Introduction"}
            {activeTab === "how-it-works" && "How It Works"}
            {activeTab === "integration" && "Integration"}
          </span>
          <ChevronDown className="h-4 w-4" />
        </Button>

        {mobileMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-background border border-t-0 rounded-b-md shadow-lg p-2 z-20">
            <Button
              variant={activeTab === "introduction" ? "default" : "ghost"}
              className="w-full justify-start mb-1"
              onClick={() => {
                setActiveTab("introduction");
                setMobileMenuOpen(false);
              }}
            >
              <FileText className="mr-2 h-4 w-4" />
              Introduction
            </Button>
            <Button
              variant={activeTab === "how-it-works" ? "default" : "ghost"}
              className="w-full justify-start mb-1"
              onClick={() => {
                setActiveTab("how-it-works");
                setMobileMenuOpen(false);
              }}
            >
              <Lightbulb className="mr-2 h-4 w-4" />
              How It Works
            </Button>
            <Button
              variant={activeTab === "integration" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => {
                setActiveTab("integration");
                setMobileMenuOpen(false);
              }}
            >
              <Code className="mr-2 h-4 w-4" />
              Integration
            </Button>
          </div>
        )}
      </div>

      {/* Main Content */}
      <main className="flex-1 container max-w-7xl mx-auto py-4 md:py-8 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-8">
          {/* Sidebar - Desktop */}
          <div className="hidden md:block md:col-span-1">
            <div className="sticky top-8">
              <nav className="space-y-1 border rounded-lg p-3 bg-card">
                <div className="text-sm font-medium text-muted-foreground mb-3 px-2">DOCUMENTATION</div>
                <Button
                  variant={activeTab === "introduction" ? "default" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => setActiveTab("introduction")}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Introduction
                </Button>
                <Button
                  variant={activeTab === "how-it-works" ? "default" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => setActiveTab("how-it-works")}
                >
                  <Lightbulb className="mr-2 h-4 w-4" />
                  How It Works
                </Button>
                <Button
                  variant={activeTab === "integration" ? "default" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => setActiveTab("integration")}
                >
                  <Code className="mr-2 h-4 w-4" />
                  Integration
                </Button>

                {activeTab === "integration" && (
                  <div className="ml-6 mt-2 space-y-1 border-l-2 border-muted pl-2">
                    <Button
                      variant={activeSubTab === "prerequisites" ? "secondary" : "ghost"}
                      size="sm"
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveSubTab("prerequisites")}
                    >
                      <ChevronRight className="mr-1 h-3 w-3" />
                      Prerequisites
                    </Button>
                    <Button
                      variant={activeSubTab === "installation" ? "secondary" : "ghost"}
                      size="sm"
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveSubTab("installation")}
                    >
                      <ChevronRight className="mr-1 h-3 w-3" />
                      Installation
                    </Button>
                    <Button
                      variant={activeSubTab === "usage" ? "secondary" : "ghost"}
                      size="sm"
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveSubTab("usage")}
                    >
                      <ChevronRight className="mr-1 h-3 w-3" />
                      Usage
                    </Button>
                    <Button
                      variant={activeSubTab === "best-practices" ? "secondary" : "ghost"}
                      size="sm"
                      className="w-full justify-start text-sm"
                      onClick={() => setActiveSubTab("best-practices")}
                    >
                      <ChevronRight className="mr-1 h-3 w-3" />
                      Best Practices
                    </Button>
                  </div>
                )}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="md:col-span-3">
            <Card className="border shadow-sm rounded-lg overflow-hidden">
              <CardContent className="p-6 md:p-8">
                {activeTab === "introduction" && (
                  <div className="prose dark:prose-invert max-w-none">
                    <h2 className="text-3xl font-bold mb-6">Introduction to AdMesh</h2>
                    <p className="text-lg mb-4">
                      AdMesh is a platform that connects AI agents with brands and products, enabling agents to provide relevant product recommendations to users while earning revenue through conversions.
                    </p>
                    <h3 className="text-xl font-semibold mt-8 mb-4">What is AdMesh?</h3>
                    <p className="mb-4">
                      AdMesh is a recommendation network designed specifically for AI agents. It allows your agent to recommend relevant products to users based on their queries, and earn revenue when those recommendations lead to conversions.
                    </p>
                    <p className="mb-4">
                      Our platform connects AI agents with brands and products across various categories, ensuring that recommendations are relevant, helpful, and valuable to users.
                    </p>
                    <h3 className="text-xl font-semibold mt-8 mb-4">Why Use AdMesh?</h3>
                    <ul className="list-disc pl-6 mb-6 space-y-2">
                      <li><strong>Monetize Your Agent:</strong> Earn revenue through product recommendations and conversions.</li>
                      <li><strong>Enhance User Experience:</strong> Provide valuable product recommendations that help users solve their problems.</li>
                      <li><strong>Easy Integration:</strong> Simple API integration with your existing agent.</li>
                      <li><strong>Transparent Analytics:</strong> Track performance, conversions, and earnings in real-time.</li>
                      <li><strong>Curated Products:</strong> Access to a growing network of quality brands and products.</li>
                    </ul>
                    <h3 className="text-xl font-semibold mt-8 mb-4">Getting Started</h3>
                    <p className="mb-4">
                      To get started with AdMesh, you&apos;ll need to:
                    </p>
                    <ol className="list-decimal pl-6 mb-6 space-y-2">
                      <li>Sign up for an AdMesh agent account</li>
                      <li>Complete the onboarding process</li>
                      <li>Integrate the AdMesh API with your agent</li>
                      <li>Start recommending products and earning revenue</li>
                    </ol>
                    <p className="mb-4">
                      The following sections will guide you through the process of understanding how AdMesh works and how to integrate it with your agent.
                    </p>
                  </div>
                )}

                {activeTab === "how-it-works" && (
                  <div className="prose dark:prose-invert max-w-none">
                    <h2 className="text-3xl font-bold mb-6">How AdMesh Works for Agents</h2>
                    <p className="text-lg mb-4">
                      AdMesh connects your AI agent with relevant products and brands, enabling you to provide valuable recommendations to your users and earn revenue through conversions.
                    </p>

                    <h3 className="text-xl font-semibold mt-8 mb-4">The AdMesh Workflow</h3>
                    <ol className="list-decimal pl-6 mb-6 space-y-4">
                      <li>
                        <strong>User Query:</strong> A user asks your agent a question that might benefit from product recommendations.
                      </li>
                      <li>
                        <strong>API Request:</strong> Your agent sends a request to the AdMesh API with the user&apos;s query and context.
                      </li>
                      <li>
                        <strong>Recommendation Generation:</strong> AdMesh analyzes the query and returns relevant product recommendations.
                      </li>
                      <li>
                        <strong>Presentation:</strong> Your agent presents these recommendations to the user in a helpful, contextual way.
                      </li>
                      <li>
                        <strong>Tracking:</strong> If the user clicks on a recommendation, AdMesh tracks this interaction.
                      </li>
                      <li>
                        <strong>Conversion:</strong> If the user makes a purchase, you earn a commission on the sale.
                      </li>
                    </ol>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Key Components</h3>

                    <h4 className="text-lg font-semibold mt-6 mb-2">1. API Keys</h4>
                    <p className="mb-4">
                      AdMesh provides two types of API keys:
                    </p>
                    <ul className="list-disc pl-6 mb-4">
                      <li><strong>Test Key:</strong> For development and testing. No real conversions or earnings.</li>
                      <li><strong>Production Key:</strong> For live environments. Real conversions and earnings.</li>
                    </ul>

                    <h4 className="text-lg font-semibold mt-6 mb-2">2. Recommendation API</h4>
                    <p className="mb-4">
                      The core of AdMesh is the recommendation API, which:
                    </p>
                    <ul className="list-disc pl-6 mb-4">
                      <li>Accepts user queries and context</li>
                      <li>Returns relevant product recommendations</li>
                      <li>Provides tracking links for each recommendation</li>
                    </ul>

                    <h4 className="text-lg font-semibold mt-6 mb-2">3. Tracking System</h4>
                    <p className="mb-4">
                      AdMesh&apos;s tracking system:
                    </p>
                    <ul className="list-disc pl-6 mb-4">
                      <li>Monitors clicks on recommendation links</li>
                      <li>Tracks conversions (purchases)</li>
                      <li>Attributes conversions to the correct agent</li>
                    </ul>

                    <h4 className="text-lg font-semibold mt-6 mb-2">4. Analytics Dashboard</h4>
                    <p className="mb-4">
                      The AdMesh dashboard provides:
                    </p>
                    <ul className="list-disc pl-6 mb-4">
                      <li>Real-time performance metrics</li>
                      <li>Conversion tracking</li>
                      <li>Earnings reports</li>
                      <li>Recommendation quality insights</li>
                    </ul>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Best Practices</h3>
                    <ul className="list-disc pl-6 mb-6 space-y-2">
                      <li><strong>Contextual Recommendations:</strong> Only recommend products when they&apos;re genuinely helpful to the user.</li>
                      <li><strong>Transparency:</strong> Be clear when you&apos;re making sponsored recommendations.</li>
                      <li><strong>Quality Over Quantity:</strong> Focus on making fewer, more relevant recommendations rather than many irrelevant ones.</li>
                      <li><strong>User Experience:</strong> Integrate recommendations seamlessly into your agent&apos;s responses.</li>
                    </ul>
                  </div>
                )}

                {activeTab === "integration" && (
                  <div className="prose dark:prose-invert max-w-none">
                    <h2 className="text-3xl font-bold mb-6">Integrating AdMesh with Your Agent</h2>
                    <p className="text-lg mb-4">
                      Follow these steps to integrate AdMesh with your AI agent and start providing product recommendations.
                    </p>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Prerequisites</h3>
                    <ul className="list-disc pl-6 mb-6 space-y-2">
                      <li>An AdMesh agent account with completed onboarding</li>
                      <li>API keys (available in your dashboard)</li>
                      <li>Your agent&apos;s implementation (API, chatbot, etc.)</li>
                    </ul>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Integration Steps</h3>

                    <div className="mb-6">
                      <h4 className="text-lg font-semibold mb-4">Choose Your SDK</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" className="flex flex-col items-center p-4 border rounded-lg hover:bg-muted transition-colors">
                          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 mb-3">
                            <Code className="h-6 w-6 text-blue-600 dark:text-blue-300" />
                          </div>
                          <h5 className="font-semibold mb-1">TypeScript SDK</h5>
                          <p className="text-sm text-center text-muted-foreground">Official TypeScript library for AdMesh API</p>
                        </a>

                        <a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" className="flex flex-col items-center p-4 border rounded-lg hover:bg-muted transition-colors">
                          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900 mb-3">
                            <Code className="h-6 w-6 text-yellow-600 dark:text-yellow-300" />
                          </div>
                          <h5 className="font-semibold mb-1">Python SDK</h5>
                          <p className="text-sm text-center text-muted-foreground">Official Python library for AdMesh API</p>
                        </a>
                      </div>
                    </div>

                    <div className="mb-8">
                      <h3 className="text-xl font-semibold mb-4">Integration Steps</h3>

                      <Tabs defaultValue={activeLang} className="w-full">
                        <TabsList className="mb-4 grid grid-cols-2 w-full">
                          <TabsTrigger
                            value="typescript"
                            onClick={() => handleLangChange("typescript")}
                            className="data-[state=active]:bg-blue-100 data-[state=active]:text-blue-800 dark:data-[state=active]:bg-blue-900 dark:data-[state=active]:text-blue-100 py-3"
                          >
                            <div className="flex items-center">
                              <Code className="mr-2 h-5 w-5" />
                              <span className="font-medium">TypeScript</span>
                            </div>
                          </TabsTrigger>
                          <TabsTrigger
                            value="python"
                            onClick={() => handleLangChange("python")}
                            className="data-[state=active]:bg-yellow-100 data-[state=active]:text-yellow-800 dark:data-[state=active]:bg-yellow-900 dark:data-[state=active]:text-yellow-100 py-3"
                          >
                            <div className="flex items-center">
                              <Code className="mr-2 h-5 w-5" />
                              <span className="font-medium">Python</span>
                            </div>
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="typescript" className="mt-0 border border-blue-100 dark:border-blue-900 rounded-md p-5 bg-blue-50/50 dark:bg-blue-950/20">
                          <div className="flex items-center mb-4">
                            <Code className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400" />
                            <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-300">TypeScript Integration</h4>
                          </div>

                          <h5 className="font-medium mb-3 text-blue-700 dark:text-blue-300">1. Installation</h5>
                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-6 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">npm</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-bash">npm install admesh</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard("npm install admesh", "ts-install")}
                            >
                              {copiedId === "ts-install" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>

                          <h5 className="font-medium mb-3 text-blue-700 dark:text-blue-300">2. Initialize the SDK</h5>
                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-6 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">TypeScript</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-typescript">{`import Admesh from 'admesh';

const client = new Admesh({
  apiKey: 'your_api_key'
});`}</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(`import Admesh from 'admesh';

const client = new Admesh({
  apiKey: 'your_api_key'
});`, "ts-init")}
                            >
                              {copiedId === "ts-init" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>

                          <h5 className="font-medium mb-3 text-blue-700 dark:text-blue-300">3. Get Recommendations</h5>
                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-6 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">TypeScript</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-typescript">{`const response = await client.recommend.getRecommendations({
  agent_id: "your_agent_id",
  query: "Best AI tools for content creation"
});

response.recommendations.forEach(rec => {
  console.log(\`Product: \${rec.title}\`);
  console.log(\`Description: \${rec.description}\`);
  console.log(\`Link: \${rec.admesh_link}\`);
});`}</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(`const response = await client.recommend.getRecommendations({
  agent_id: "your_agent_id",
  query: "Best AI tools for content creation"
});

response.recommendations.forEach(rec => {
  console.log(\`Product: \${rec.title}\`);
  console.log(\`Description: \${rec.description}\`);
  console.log(\`Link: \${rec.admesh_link}\`);
});`, "ts-rec")}
                            >
                              {copiedId === "ts-rec" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>
                        </TabsContent>

                        <TabsContent value="python" className="mt-0 border border-yellow-100 dark:border-yellow-900 rounded-md p-5 bg-yellow-50/50 dark:bg-yellow-950/20">
                          <div className="flex items-center mb-4">
                            <Code className="h-6 w-6 mr-2 text-yellow-600 dark:text-yellow-400" />
                            <h4 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300">Python Integration</h4>
                          </div>

                          <h5 className="font-medium mb-3 text-yellow-700 dark:text-yellow-300">1. Installation</h5>
                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-6 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">pip</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-bash">pip install admesh-python</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard("pip install admesh-python", "py-install")}
                            >
                              {copiedId === "py-install" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>

                          <h5 className="font-medium mb-3 text-yellow-700 dark:text-yellow-300">2. Initialize the SDK</h5>
                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-6 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">Python</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-python">{`import admesh

client = admesh.Admesh(api_key="your_api_key")`}</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(`import admesh

client = admesh.Admesh(api_key="your_api_key")`, "py-init")}
                            >
                              {copiedId === "py-init" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>

                          <h5 className="font-medium mb-3 text-yellow-700 dark:text-yellow-300">3. Get Recommendations</h5>
                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-6 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">Python</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-python">{`response = client.recommend.get_recommendations(
    agent_id="your_agent_id",
    query="Best AI tools for content creation"
)

for rec in response.recommendations:
    print(f"Product: {rec.title}")
    print(f"Description: {rec.description}")
    print(f"Link: {rec.admesh_link)")`}</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(`response = client.recommend.get_recommendations(
    agent_id="your_agent_id",
    query="Best AI tools for content creation"
)

for rec in response.recommendations:
    print(f"Product: {rec.title}")
    print(f"Description: {rec.description}")
    print(f"Link: {rec.admesh_link)")`, "py-rec")}
                            >
                              {copiedId === "py-rec" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>

                    <div className="mb-8">
                      <h3 className="text-xl font-semibold mb-4">Direct API Integration</h3>
                      <p className="mb-4">
                        If you prefer to use the API directly without an SDK, you can make HTTP requests to our endpoints:
                      </p>

                      <Tabs defaultValue="curl" className="w-full">
                        <TabsList className="mb-4 grid grid-cols-2 w-full">
                          <TabsTrigger
                            value="curl"
                            className="data-[state=active]:bg-gray-100 data-[state=active]:text-gray-800 dark:data-[state=active]:bg-gray-800 dark:data-[state=active]:text-gray-100 py-3"
                          >
                            <div className="flex items-center">
                              <Code className="mr-2 h-5 w-5" />
                              <span className="font-medium">cURL</span>
                            </div>
                          </TabsTrigger>
                          <TabsTrigger
                            value="fetch"
                            className="data-[state=active]:bg-gray-100 data-[state=active]:text-gray-800 dark:data-[state=active]:bg-gray-800 dark:data-[state=active]:text-gray-100 py-3"
                          >
                            <div className="flex items-center">
                              <Code className="mr-2 h-5 w-5" />
                              <span className="font-medium">Fetch API</span>
                            </div>
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="curl" className="mt-0 border border-gray-200 dark:border-gray-800 rounded-md p-5 bg-gray-50/50 dark:bg-gray-900/20">
                          <div className="flex items-center mb-4">
                            <Code className="h-6 w-6 mr-2 text-gray-600 dark:text-gray-400" />
                            <h4 className="text-lg font-semibold">cURL Example</h4>
                          </div>

                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-4 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">bash</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-bash">{`curl -X POST https://api.useadmesh.com/recommend \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your_api_key" \\
  -d '{
    "query": "Best AI tools for content creation",
    "agent_id": "your_agent_id"
  }'`}</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(`curl -X POST https://api.useadmesh.com/agent/recommend \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your_api_key" \\
  -d '{
    "query": "Best AI tools for content creation",
    "format": "auto"
  }'`, "curl-api")}
                            >
                              {copiedId === "curl-api" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>
                        </TabsContent>

                        <TabsContent value="fetch" className="mt-0 border border-gray-200 dark:border-gray-800 rounded-md p-5 bg-gray-50/50 dark:bg-gray-900/20">
                          <div className="flex items-center mb-4">
                            <Code className="h-6 w-6 mr-2 text-gray-600 dark:text-gray-400" />
                            <h4 className="text-lg font-semibold">Fetch API Example</h4>
                          </div>

                          <div className="bg-white dark:bg-gray-900 rounded-md p-4 mb-4 relative overflow-hidden shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <Code className="h-4 w-4 mr-2" />
                                <span className="text-xs font-medium">javascript</span>
                              </div>
                            </div>
                            <pre className="text-sm overflow-x-auto bg-gray-50 dark:bg-gray-950 p-3 rounded"><code className="language-javascript">{`const response = await fetch('https://api.useadmesh.com/agent/recommend', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_api_key'
  },
  body: JSON.stringify({
    query: 'Best AI tools for content creation',
    format: 'auto'
  })
});

const data = await response.json();
console.log(data);`}</code></pre>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="absolute top-2 right-2"
                              onClick={() => copyToClipboard(`const response = await fetch('https://api.useadmesh.com/agent/recommend', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_api_key'
  },
  body: JSON.stringify({
    query: 'Best AI tools for content creation',
    format: 'auto'
  })
});

const data = await response.json();
console.log(data);`, "fetch-api")}
                            >
                              {copiedId === "fetch-api" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                            </Button>
                          </div>
                        </TabsContent>
                      </Tabs>

                      <p className="text-sm text-muted-foreground mt-4">
                        For more details on API endpoints and parameters, refer to our <a href="#" className="text-primary hover:underline">API Reference</a>.
                      </p>
                    </div>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Testing Your Integration</h3>
                    <p className="mb-4">
                      To test your integration:
                    </p>
                    <ol className="list-decimal pl-6 mb-6 space-y-2">
                      <li>Use your test API key for development</li>
                      <li>Make recommendation requests with various queries</li>
                      <li>Click on the tracking links to simulate user interactions</li>
                      <li>Check your dashboard to verify that clicks are being tracked</li>
                    </ol>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Going Live</h3>
                    <p className="mb-4">
                      When you&apos;re ready to go live:
                    </p>
                    <ol className="list-decimal pl-6 mb-6 space-y-2">
                      <li>Switch to your production API key</li>
                      <li>Ensure your agent is presenting recommendations appropriately</li>
                      <li>Monitor your dashboard for performance metrics</li>
                    </ol>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Best Practices for Agent UI/UX</h3>
                    <p className="mb-4">
                      Integrating product recommendations into your agent&apos;s responses requires careful consideration of user experience. Here are some best practices:
                    </p>

                    <h4 className="text-lg font-semibold mt-6 mb-2">1. Contextual Recommendations</h4>
                    <p className="mb-4">
                      Only provide recommendations when they&apos;re genuinely relevant to the user&apos;s query. Irrelevant recommendations can damage trust in your agent.
                    </p>
                    <div className="bg-muted rounded-md p-4 mb-4">
                      <p className="font-medium">Example:</p>
                      <p className="text-sm mt-2"><strong>User:</strong> &quot;What are some good tools for video editing?&quot;</p>
                      <p className="text-sm mt-1"><strong>Agent:</strong> &quot;For video editing, I&apos;d recommend considering tools like Adobe Premiere Pro for professional editing, DaVinci Resolve for color grading, or Filmora for beginners. Based on your needs, [Product Name] offers an excellent balance of features and ease of use. It includes [key features] and is popular among [target audience]. You can check it out here: [tracking link]&quot;</p>
                    </div>

                    <h4 className="text-lg font-semibold mt-6 mb-2">2. Transparent Disclosure</h4>
                    <p className="mb-4">
                      Always be transparent about sponsored or monetized recommendations. This builds trust with users and complies with advertising regulations.
                    </p>
                    <div className="bg-muted rounded-md p-4 mb-4">
                      <p className="font-medium">Example disclosure:</p>
                      <p className="text-sm mt-2">&quot;Here are some recommended products that might help (affiliate links):&quot;</p>
                      <p className="text-sm mt-2">&quot;I can suggest some tools that might be useful (I may earn a commission if you purchase through these links):&quot;</p>
                    </div>

                    <h4 className="text-lg font-semibold mt-6 mb-2">3. Natural Integration</h4>
                    <p className="mb-4">
                      Integrate recommendations naturally into your agent&apos;s responses rather than making them feel like intrusive advertisements.
                    </p>
                    <div className="bg-muted rounded-md p-4 mb-4">
                      <p className="font-medium">Instead of:</p>
                      <p className="text-sm mt-2">&quot;BUY THIS PRODUCT NOW! CLICK HERE!&quot;</p>
                      <p className="font-medium mt-3">Try:</p>
                      <p className="text-sm mt-2">&quot;Based on your requirements, you might find [Product Name] helpful. It offers [specific features that address the user&apos;s needs] and has received positive reviews for [relevant benefit].&quot;</p>
                    </div>

                    <h4 className="text-lg font-semibold mt-6 mb-2">4. Provide Value First</h4>
                    <p className="mb-4">
                      Always provide valuable information before making recommendations. This establishes your agent as a helpful resource rather than just a sales channel.
                    </p>
                    <div className="bg-muted rounded-md p-4 mb-4">
                      <p className="font-medium">Example structure:</p>
                      <ol className="list-decimal pl-6 text-sm mt-2">
                        <li>Answer the user&apos;s question thoroughly</li>
                        <li>Provide general advice or information</li>
                        <li>Then introduce relevant product recommendations</li>
                      </ol>
                    </div>

                    <h4 className="text-lg font-semibold mt-6 mb-2">5. Format for Readability</h4>
                    <p className="mb-4">
                      Format your recommendations in a way that&apos;s easy to read and understand. Use clear headings, bullet points, and concise descriptions.
                    </p>
                    <div className="bg-muted rounded-md p-4 mb-4">
                      <p className="font-medium">Example format:</p>
                      <div className="text-sm mt-2">
                        <p><strong>Recommended Tool: [Product Name]</strong></p>
                        <ul className="list-disc pl-6 mt-1">
                          <li>Best for: [specific use case]</li>
                          <li>Key features: [feature 1], [feature 2], [feature 3]</li>
                          <li>Price: [price or pricing model]</li>
                          <li><a href="#" className="text-primary">Learn more</a> (affiliate link)</li>
                        </ul>
                      </div>
                    </div>

                    <h3 className="text-xl font-semibold mt-8 mb-4">Support</h3>
                    <p className="mb-4">
                      If you need help with integration or have questions about best practices, please contact our support team at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}

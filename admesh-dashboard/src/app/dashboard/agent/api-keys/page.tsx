"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import { Co<PERSON>, Loader2, Key, AlertCircle, RefreshCw, Eye, EyeOff } from "lucide-react";
import DashboardFooter from "@/components/DashboardFooter";

interface ApiKey {
  id: string;
  key: string;
  type: string;
  created_at: number;
  last_used: number | null;
  is_active: boolean;
  name: string;
}

// Helper component for displaying API keys
const ApiKeyDisplay = ({
  keyData,
  keyType,
  showKey,
  setShow<PERSON>ey,
  onGenerate,
  isGenerating,
  formatDate
}: {
  keyData: ApiKey | null;
  keyType: "test" | "production";
  showKey: boolean;
  setShowKey: (show: boolean) => void;
  onGenerate: (keyType: "test" | "production") => void;
  isGenerating: boolean;
  formatDate: (timestamp: number | null) => string;
}) => {
  return (
    <div className="space-y-6">
      {keyData ? (
        <>
          <div className="space-y-3">
            <Label className="text-sm font-medium">API Key</Label>
            <div className="relative">
              <Input
                value={showKey ? keyData.key : "•".repeat(40)}
                readOnly
                className="h-12 pr-32 font-mono text-sm bg-muted/50"
              />
              <div className="absolute right-2 top-2 flex gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowKey(!showKey)}
                  className="h-8 w-8 p-0"
                >
                  {showKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    navigator.clipboard.writeText(keyData.key);
                    toast.success(`${keyType === "test" ? "Test" : "Production"} API Key copied to clipboard`);
                  }}
                  className="h-8 w-8 p-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Created</Label>
              <p className="text-sm font-mono bg-muted/30 p-3 rounded-md">{formatDate(keyData.created_at)}</p>
            </div>
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Last Used</Label>
              <p className="text-sm font-mono bg-muted/30 p-3 rounded-md">{formatDate(keyData.last_used)}</p>
            </div>
          </div>

          <div className="pt-4 border-t">
            <Button
              onClick={() => onGenerate(keyType)}
              disabled={isGenerating}
              variant="outline"
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Regenerating...
                </>
              ) : (
                <>Regenerate {keyType === "test" ? "Test" : "Production"} Key</>
              )}
            </Button>
          </div>
        </>
      ) : (
        <>
          <div className="text-center py-12">
            <div className="h-16 w-16 mx-auto bg-muted rounded-full flex items-center justify-center mb-4">
              <Key className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No {keyType === "test" ? "Test" : "Production"} API Key</h3>
            <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
              Generate a {keyType === "test" ? "test" : "production"} API key to start integrating with AdMesh.
            </p>
            <Button
              onClick={() => onGenerate(keyType)}
              disabled={isGenerating}
              className="w-full max-w-xs"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                </>
              ) : (
                <>Generate {keyType === "test" ? "Test" : "Production"} API Key</>
              )}
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default function ApiKeysPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testKey, setTestKey] = useState<ApiKey | null>(null);
  const [productionKey, setProductionKey] = useState<ApiKey | null>(null);
  const [isGeneratingTestKey, setIsGeneratingTestKey] = useState(false);
  const [isGeneratingProductionKey, setIsGeneratingProductionKey] = useState(false);
  const [agentId, setAgentId] = useState("");
  const [showTestKey, setShowTestKey] = useState(false);
  const [showProductionKey, setShowProductionKey] = useState(false);
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.useadmesh.com";

  const fetchApiKeys = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();

      // First get the agent data to get the agent_id
      const agentResponse = await fetch(`${apiBaseUrl}/agent/onboarding/data`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!agentResponse.ok) {
        if (agentResponse.status === 403) {
          throw new Error("You don't have permission to access this data");
        } else if (agentResponse.status === 404) {
          throw new Error("Agent profile not found");
        } else {
          throw new Error("Failed to fetch agent data");
        }
      }

      const agentData = await agentResponse.json();
      // Use agent_id from response, or fall back to user.uid since they should be the same
      setAgentId(agentData.agent_id || user.uid);

      // Now get the API keys
      const keysResponse = await fetch(`${apiBaseUrl}/api-keys/list`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!keysResponse.ok) {
        if (keysResponse.status === 403) {
          throw new Error("You don't have permission to access API keys");
        } else if (keysResponse.status >= 500) {
          throw new Error("Server error. Please try again later");
        } else {
          throw new Error(`Failed to fetch API keys (${keysResponse.status})`);
        }
      }

      const keysData = await keysResponse.json();

      // Filter active keys by type
      const activeKeys = keysData.keys.filter((key: ApiKey) => key.is_active);
      const testKeyData = activeKeys.find((key: ApiKey) => key.type === "test") || null;
      const productionKeyData = activeKeys.find((key: ApiKey) => key.type === "production") || null;

      setTestKey(testKeyData);
      setProductionKey(productionKeyData);
    } catch (error) {
      console.error("Error fetching API keys:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to load API keys";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user, apiBaseUrl]);

  useEffect(() => {
    if (!user) return;
    fetchApiKeys();
  }, [user, fetchApiKeys]);

  const generateApiKey = async (keyType: "test" | "production") => {
    const isTest = keyType === "test";
    if (isTest) {
      setIsGeneratingTestKey(true);
    } else {
      setIsGeneratingProductionKey(true);
    }

    try {
      const token = await user?.getIdToken();
      if (!token) throw new Error("Not authenticated");

      const response = await fetch(`${apiBaseUrl}/api-keys/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          agent_id: agentId,
          type: keyType,
          name: `${keyType.charAt(0).toUpperCase() + keyType.slice(1)} API Key`
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `Failed to generate ${keyType} API key`);
      }

      const keyData = await response.json();

      if (isTest) {
        setTestKey(keyData);
        toast.success("Test API key generated successfully");
      } else {
        setProductionKey(keyData);
        toast.success("Production API key generated successfully");
      }

      // Update agent document with the API key ID
      await fetch(`${apiBaseUrl}/agent/update-api-key`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          agent_id: agentId,
          key_type: keyType,
          key_id: keyData.id
        })
      });
    } catch (error) {
      console.error(`Error generating ${keyType} API key:`, error);
      toast.error(`Failed to generate ${keyType} API key: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      if (isTest) {
        setIsGeneratingTestKey(false);
      } else {
        setIsGeneratingProductionKey(false);
      }
    }
  };

  const formatDate = (timestamp: number | null) => {
    if (!timestamp) return "Never";
    return new Date(timestamp * 1000).toLocaleString();
  };



  if (loading) {
    return (
      <div className="container max-w-4xl mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">API Keys</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Manage your API keys for integrating with AdMesh</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchApiKeys}
          disabled={loading}
          className="gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchApiKeys}
              className="ml-4"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="sandbox" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger value="sandbox">Test Environment</TabsTrigger>
          <TabsTrigger value="production">Production</TabsTrigger>
        </TabsList>

        <TabsContent value="sandbox">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" /> Test API Key
              </CardTitle>
              <CardDescription>
                Use this key for testing. It won&apos;t affect your production data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApiKeyDisplay
                keyData={testKey}
                keyType="test"
                showKey={showTestKey}
                setShowKey={setShowTestKey}
                onGenerate={generateApiKey}
                isGenerating={isGeneratingTestKey}
                formatDate={formatDate}
              />

              <Alert className="mt-6" variant="warning">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Test Environment</AlertTitle>
                <AlertDescription>
                  Test keys are for development purposes only. Any data created with test keys will be marked as test data.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="production">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" /> Production API Key
              </CardTitle>
              <CardDescription>
                Use this key for your production environment. Keep it secure.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ApiKeyDisplay
                keyData={productionKey}
                keyType="production"
                showKey={showProductionKey}
                setShowKey={setShowProductionKey}
                onGenerate={generateApiKey}
                isGenerating={isGeneratingProductionKey}
                formatDate={formatDate}
              />

              <Alert className="mt-6" variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Production Environment</AlertTitle>
                <AlertDescription>
                  This key has full access to your account. Never share it publicly or include it in client-side code.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dashboard Footer */}
      <DashboardFooter />
    </div>
  );
}

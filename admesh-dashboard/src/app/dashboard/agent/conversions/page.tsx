"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, AlertCircle, RefreshCw } from "lucide-react";
import DashboardFooter from "@/components/DashboardFooter";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

interface Conversion {
  id: string;
  timestamp: string;
  brand_id: string;
  offer_id: string;
  value: number;
  currency: string;
  status: string;
  event_type: string;
  brand_name?: string;
  offer_title?: string;
}

export default function ConversionsPage() {
  const { user } = useAuth();
  const [conversions, setConversions] = useState<Conversion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30d");
  const [statusFilter, setStatusFilter] = useState("all");
  const [stats, setStats] = useState({
    totalConversions: 0,
    confirmedConversions: 0,
    pendingConversions: 0,
    totalValue: 0,
  });

  const fetchConversions = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/conversions?time_range=${timeRange}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error("You don't have permission to access conversions data");
        } else if (response.status === 404) {
          throw new Error("Conversions data not found");
        } else if (response.status >= 500) {
          throw new Error("Server error. Please try again later");
        } else {
          throw new Error(`Failed to fetch conversions (${response.status})`);
        }
      }

      const data = await response.json();
      setConversions(data.conversions || []);

        // Calculate stats
        const totalConversions = data.conversions?.length || 0;
        const confirmedConversions = data.conversions?.filter(
          (c: Conversion) => c.status === "confirmed" || c.status === "verified"
        ).length || 0;
        const pendingConversions = data.conversions?.filter(
          (c: Conversion) => c.status === "pending"
        ).length || 0;
        const totalValue = data.conversions?.reduce(
          (sum: number, c: Conversion) => sum + c.value,
          0
        ) || 0;

        setStats({
          totalConversions,
          confirmedConversions,
          pendingConversions,
          totalValue,
        });
      } catch (error) {
        console.error("Error fetching conversions:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to fetch conversions";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

  useEffect(() => {
    fetchConversions();
  }, [user, timeRange]);

  const filteredConversions = conversions.filter((conversion) => {
    if (statusFilter === "all") return true;
    return conversion.status === statusFilter;
  });

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      minimumFractionDigits: 2,
    }).format(amount / 100); // Assuming amount is stored in cents
  };

  const renderSkeleton = () => (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-4 w-32 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Conversions</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Track your conversion performance and revenue</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchConversions}
            disabled={loading}
            className="gap-2 w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchConversions}
              className="ml-4"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {loading ? (
        renderSkeleton()
      ) : (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Conversions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalConversions}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  In selected period
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Confirmed
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.confirmedConversions}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Verified conversions
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Pending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.pendingConversions}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Awaiting verification
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Value
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  From all conversions
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Conversions Table */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Conversion History</CardTitle>
                <CardDescription>
                  A detailed breakdown of your conversions
                </CardDescription>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Brand</TableHead>
                    <TableHead>Offer</TableHead>
                    <TableHead>Event Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredConversions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6">
                        No conversions found for the selected criteria.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredConversions.map((conversion) => (
                      <TableRow key={conversion.id}>
                        <TableCell>
                          {format(new Date(conversion.timestamp), "MMM d, yyyy")}
                        </TableCell>
                        <TableCell>{conversion.brand_name || conversion.brand_id}</TableCell>
                        <TableCell>{conversion.offer_title || conversion.offer_id}</TableCell>
                        <TableCell className="capitalize">{conversion.event_type}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              conversion.status === "confirmed" || conversion.status === "verified"
                                ? "default"
                                : conversion.status === "pending"
                                ? "outline"
                                : "destructive"
                            }
                            className={
                              conversion.status === "confirmed" || conversion.status === "verified"
                                ? "bg-green-500 text-white"
                                : ""
                            }
                          >
                            {conversion.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(conversion.value, conversion.currency)}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </>
      )}

      {/* Dashboard Footer */}
      <DashboardFooter />
    </div>
  );
}

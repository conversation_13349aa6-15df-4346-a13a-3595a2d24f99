"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import { toast } from "sonner";
import {
  PlayCircle,
  Sparkles,
  Send,
  Loader2,
  MessageCircle,
  Lightbulb,
  Zap,
  Copy
} from "lucide-react";

import { ADDITIONAL_DEMO_DATA } from "./demo-data";
import DashboardFooter from "@/components/DashboardFooter";

// Import AdMesh UI SDK components
import {
  AdMeshProductCard,
  AdMeshConversationSummary,
  AdMeshInlineRecommendation
} from "admesh-ui-sdk";

interface Recommendation {
  ad_id: string;
  admesh_link: string;
  product_id: string;
  reason: string;
  title: string;
  intent_match_score?: number;
  features?: string[];
  has_free_tier?: boolean;
  integrations?: string[];
  pricing?: string;
  redirect_url?: string;
  reviews_summary?: string;
  reward_note?: string | null;
  security?: string[];
  slug?: string;
  support?: string[];
  trial_days?: number;
  url?: string;
  keywords?: string[];
  badges?: string[];
}

interface RecommendationResponse {
  session_id: string;
  intent: Record<string, unknown>;
  response: {
    summary: string;
    recommendations: Recommendation[];
    followup_suggestions: string[];
  };
  tokens_used: number;
  model_used: string;
  recommendation_id: string;
  end_of_session: boolean;
}

// Demo queries with realistic API response data
const DEMO_DATA: Record<string, RecommendationResponse> = {
  "Best CRM for remote teams": {
    session_id: "demo_session_crm_001",
    intent: {
      goal: "Find CRM software suitable for remote teams",
      categories: ["CRM", "Sales Tools", "Customer Management"],
      keywords: ["CRM", "remote", "teams", "collaboration", "sales"],
      llm_intent_confidence_score: 0.92
    },
    response: {
      summary: "Based on your need for CRM solutions for remote teams, I've found several excellent options that offer strong collaboration features, cloud-based access, and team management capabilities.",
      recommendations: [
        {
          ad_id: "hubspot-crm-001",
          admesh_link: "https://useadmesh.com/track?ad_id=hubspot-crm-001",
          product_id: "hubspot-crm",
          reason: "Perfect for remote teams with excellent collaboration features, free tier availability, and comprehensive integration ecosystem",
          title: "HubSpot CRM",
          intent_match_score: 0.94,
          features: ["Contact Management", "Email Marketing", "Sales Pipeline", "Team Collaboration", "Mobile App"],
          has_free_tier: true,
          integrations: ["Gmail", "Outlook", "Slack", "Zoom", "Microsoft Teams"],
          pricing: "Free - $1,200/month",
          trial_days: 14,
          reviews_summary: "Highly rated for ease of use and excellent customer support",
          security: ["SOC 2", "GDPR Compliant", "SSL Encryption"],
          support: ["24/7 Chat", "Phone Support", "Knowledge Base"],
          keywords: ["CRM", "Sales", "Remote Teams", "Collaboration"],
          badges: ["Top Match", "Free Tier"],
          url: "https://hubspot.com"
        },
        {
          ad_id: "salesforce-001",
          admesh_link: "https://useadmesh.com/track?ad_id=salesforce-001",
          product_id: "salesforce-crm",
          reason: "Industry-leading CRM with powerful automation and extensive customization options for growing remote teams",
          title: "Salesforce CRM",
          intent_match_score: 0.89,
          features: ["Advanced Analytics", "Workflow Automation", "Custom Fields", "API Access", "Mobile CRM"],
          has_free_tier: false,
          integrations: ["Gmail", "Outlook", "Slack", "Zoom", "Mailchimp"],
          pricing: "$25 - $300/user/month",
          trial_days: 30,
          reviews_summary: "Powerful platform with steep learning curve but excellent ROI",
          security: ["SOC 2", "ISO 27001", "GDPR Compliant"],
          support: ["24/7 Support", "Trailhead Training", "Community"],
          keywords: ["CRM", "Enterprise", "Automation", "Analytics"],
          badges: ["Popular", "Trial Available"],
          url: "https://salesforce.com"
        },
        {
          ad_id: "pipedrive-001",
          admesh_link: "https://useadmesh.com/track?ad_id=pipedrive-001",
          product_id: "pipedrive-crm",
          reason: "Simple, visual CRM designed for sales teams with excellent mobile support and intuitive interface",
          title: "Pipedrive",
          intent_match_score: 0.87,
          features: ["Visual Pipeline", "Activity Reminders", "Email Integration", "Mobile App", "Sales Reporting"],
          has_free_tier: false,
          integrations: ["Gmail", "Outlook", "Slack", "Zoom", "Mailchimp"],
          pricing: "$14.90 - $99/user/month",
          trial_days: 14,
          reviews_summary: "Loved by sales teams for its simplicity and visual approach",
          security: ["SOC 2", "GDPR Compliant", "SSL Encryption"],
          support: ["Email Support", "Phone Support", "Knowledge Base"],
          keywords: ["CRM", "Sales", "Visual", "Pipeline"],
          badges: ["Trial Available"],
          url: "https://pipedrive.com"
        }
      ],
      followup_suggestions: [
        "CRM integration with project management tools",
        "Sales automation for remote teams",
        "Customer support tools for distributed teams"
      ]
    },
    tokens_used: 450,
    model_used: "mistralai/mistral-7b-instruct",
    recommendation_id: "rec_crm_demo_001",
    end_of_session: true
  },

  "Project management tools for startups": {
    session_id: "demo_session_pm_002",
    intent: {
      goal: "Find project management tools suitable for startups",
      categories: ["Project Management", "Productivity", "Team Collaboration"],
      keywords: ["project management", "startups", "agile", "collaboration", "planning"],
      llm_intent_confidence_score: 0.90
    },
    response: {
      summary: "For startups looking for project management solutions, I've identified tools that offer excellent value, scalability, and team collaboration features perfect for growing companies.",
      recommendations: [
        {
          ad_id: "notion-pm-001",
          admesh_link: "https://useadmesh.com/track?ad_id=notion-pm-001",
          product_id: "notion",
          reason: "All-in-one workspace perfect for startups with flexible project management, documentation, and collaboration features",
          title: "Notion",
          intent_match_score: 0.92,
          features: ["Project Templates", "Database Management", "Team Wiki", "Task Management", "Real-time Collaboration"],
          has_free_tier: true,
          integrations: ["Slack", "Google Drive", "Figma", "GitHub", "Zapier"],
          pricing: "Free - $10/user/month",
          trial_days: 0,
          reviews_summary: "Highly flexible platform loved by startups for its versatility",
          security: ["SOC 2", "GDPR Compliant", "SSL Encryption"],
          support: ["Email Support", "Community", "Help Center"],
          keywords: ["Project Management", "Workspace", "Documentation", "Startups"],
          badges: ["Top Match", "Free Tier"],
          url: "https://notion.so"
        },
        {
          ad_id: "asana-pm-001",
          admesh_link: "https://useadmesh.com/track?ad_id=asana-pm-001",
          product_id: "asana",
          reason: "Intuitive project management with excellent free tier and powerful features for growing teams",
          title: "Asana",
          intent_match_score: 0.89,
          features: ["Project Templates", "Timeline View", "Custom Fields", "Goal Tracking", "Portfolio Management"],
          has_free_tier: true,
          integrations: ["Slack", "Microsoft Teams", "Adobe Creative Cloud", "Salesforce", "Zoom"],
          pricing: "Free - $24.99/user/month",
          trial_days: 30,
          reviews_summary: "Excellent for team coordination and project visibility",
          security: ["SOC 2", "ISO 27001", "GDPR Compliant"],
          support: ["Email Support", "Live Chat", "Training Resources"],
          keywords: ["Project Management", "Teams", "Timeline", "Goals"],
          badges: ["Popular", "Free Tier"],
          url: "https://asana.com"
        },
        {
          ad_id: "trello-pm-001",
          admesh_link: "https://useadmesh.com/track?ad_id=trello-pm-001",
          product_id: "trello",
          reason: "Simple, visual project management using Kanban boards, perfect for small startup teams",
          title: "Trello",
          intent_match_score: 0.85,
          features: ["Kanban Boards", "Card Management", "Power-Ups", "Team Collaboration", "Mobile App"],
          has_free_tier: true,
          integrations: ["Slack", "Google Drive", "Dropbox", "GitHub", "Mailchimp"],
          pricing: "Free - $17.50/user/month",
          trial_days: 14,
          reviews_summary: "Simple and intuitive, great for visual project management",
          security: ["SOC 2", "GDPR Compliant", "SSL Encryption"],
          support: ["Email Support", "Help Center", "Community"],
          keywords: ["Project Management", "Kanban", "Visual", "Simple"],
          badges: ["Free Tier"],
          url: "https://trello.com"
        }
      ],
      followup_suggestions: [
        "Time tracking tools for project management",
        "Agile development tools for startups",
        "Team communication platforms"
      ]
    },
    tokens_used: 420,
    model_used: "mistralai/mistral-7b-instruct",
    recommendation_id: "rec_pm_demo_002",
    end_of_session: true
  },

  "Email marketing platforms with automation": {
    session_id: "demo_session_email_003",
    intent: {
      goal: "Find email marketing platforms with automation capabilities",
      categories: ["Email Marketing", "Marketing Automation", "Digital Marketing"],
      keywords: ["email marketing", "automation", "campaigns", "newsletters", "drip campaigns"],
      llm_intent_confidence_score: 0.93
    },
    response: {
      summary: "I've found excellent email marketing platforms that offer powerful automation features, helping you create sophisticated email campaigns and nurture leads effectively.",
      recommendations: [
        {
          ad_id: "mailchimp-001",
          admesh_link: "https://useadmesh.com/track?ad_id=mailchimp-001",
          product_id: "mailchimp",
          reason: "Leading email marketing platform with intuitive automation builder and comprehensive analytics",
          title: "Mailchimp",
          intent_match_score: 0.94,
          features: ["Drag-and-Drop Builder", "Automation Workflows", "A/B Testing", "Analytics", "Segmentation"],
          has_free_tier: true,
          integrations: ["Shopify", "WordPress", "Salesforce", "Facebook", "Instagram"],
          pricing: "Free - $350/month",
          trial_days: 30,
          reviews_summary: "User-friendly platform with excellent deliverability rates",
          security: ["SOC 2", "GDPR Compliant", "SSL Encryption"],
          support: ["Email Support", "Live Chat", "Knowledge Base"],
          keywords: ["Email Marketing", "Automation", "Analytics", "Campaigns"],
          badges: ["Top Match", "Free Tier"],
          url: "https://mailchimp.com"
        },
        {
          ad_id: "convertkit-001",
          admesh_link: "https://useadmesh.com/track?ad_id=convertkit-001",
          product_id: "convertkit",
          reason: "Creator-focused email marketing with powerful automation and tagging system",
          title: "ConvertKit",
          intent_match_score: 0.90,
          features: ["Visual Automation", "Tagging System", "Landing Pages", "Forms", "Subscriber Management"],
          has_free_tier: true,
          integrations: ["WordPress", "Shopify", "Stripe", "PayPal", "Zapier"],
          pricing: "Free - $79/month",
          trial_days: 14,
          reviews_summary: "Excellent for creators and course sellers with advanced segmentation",
          security: ["GDPR Compliant", "SSL Encryption", "Data Protection"],
          support: ["Email Support", "Live Chat", "Creator Resources"],
          keywords: ["Email Marketing", "Creators", "Automation", "Tagging"],
          badges: ["Popular", "Free Tier"],
          url: "https://convertkit.com"
        },
        {
          ad_id: "activecampaign-001",
          admesh_link: "https://useadmesh.com/track?ad_id=activecampaign-001",
          product_id: "activecampaign",
          reason: "Advanced marketing automation with CRM integration and behavioral triggers",
          title: "ActiveCampaign",
          intent_match_score: 0.88,
          features: ["Advanced Automation", "CRM Integration", "Behavioral Triggers", "Machine Learning", "Split Testing"],
          has_free_tier: false,
          integrations: ["Shopify", "WordPress", "Salesforce", "Facebook", "Google Analytics"],
          pricing: "$15 - $229/month",
          trial_days: 14,
          reviews_summary: "Powerful automation capabilities with steep learning curve",
          security: ["SOC 2", "GDPR Compliant", "ISO 27001"],
          support: ["Live Chat", "Phone Support", "Training"],
          keywords: ["Email Marketing", "Automation", "CRM", "Advanced"],
          badges: ["AI Powered", "Trial Available"],
          url: "https://activecampaign.com"
        }
      ],
      followup_suggestions: [
        "Email deliverability optimization tools",
        "Landing page builders for email campaigns",
        "Customer segmentation strategies"
      ]
    },
    tokens_used: 440,
    model_used: "mistralai/mistral-7b-instruct",
    recommendation_id: "rec_email_demo_003",
    end_of_session: true
  },

  // Merge additional demo data
  ...ADDITIONAL_DEMO_DATA
};

// Extract query names for the UI
const DEMO_QUERIES = Object.keys(DEMO_DATA);

export default function DemoPage() {
  const [selectedQuery, setSelectedQuery] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [responseData, setResponseData] = useState<RecommendationResponse | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<string>("card");

  const loadDemoData = async (query: string) => {
    setIsLoading(true);
    setRecommendations([]);
    setResponseData(null);

    try {
      // Simulate API call delay for realistic experience
      await new Promise(resolve => setTimeout(resolve, 1000));

      const demoResponse = DEMO_DATA[query];
      if (demoResponse) {
        setResponseData(demoResponse);
        setRecommendations(demoResponse.response.recommendations || []);
        toast.success(`Loaded ${demoResponse.response.recommendations.length} demo recommendations`);
      } else {
        throw new Error("Demo data not found for this query");
      }
    } catch (err) {
      console.error("Error loading demo data:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to load demo data";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestedQueryClick = (query: string) => {
    setSelectedQuery(query);
    loadDemoData(query);
  };

  return (
    <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-3 sm:space-y-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
          <div className="flex items-center gap-2">
            <PlayCircle className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            <h1 className="text-2xl sm:text-3xl font-bold">Try AdMesh Demo</h1>
          </div>
          <Badge variant="secondary" className="sm:ml-2">
            <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            Interactive
          </Badge>
        </div>
        <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto px-4 sm:px-0">
          Experience real AdMesh recommendations with our interactive demo. Select any query to see live results in different ad formats.
        </p>
      </div>

      {/* Query Input Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Interactive Demo
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Select a query to see real AdMesh recommendations displayed in different ad formats.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Demo Queries */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                <label className="text-sm font-medium">Demo Queries</label>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {DEMO_QUERIES.map((query, index) => (
                  <Button
                    key={index}
                    variant={selectedQuery === query ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleSuggestedQueryClick(query)}
                    disabled={isLoading}
                    className="text-left justify-start h-auto py-3 px-3"
                  >
                    <div className="flex items-center gap-2">
                      {isLoading && selectedQuery === query ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                      <span>{query}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Results Display Section */}
      {recommendations.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                API Results ({recommendations.length} recommendations)
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Live results from your test API key. Choose different ad formats to see how recommendations appear.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Format Selector */}
              <Tabs value={selectedFormat} onValueChange={setSelectedFormat}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="card">Product Card</TabsTrigger>
                  <TabsTrigger value="inline">Inline</TabsTrigger>
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                </TabsList>

                <TabsContent value="card" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {recommendations.slice(0, 6).map((rec, index) => (
                      <AdMeshProductCard
                        key={index}
                        recommendation={rec}
                        onClick={(adId: string, admeshLink: string) => window.open(admeshLink, '_blank')}
                        showFeatures={true}
                        showPricing={true}
                        compact={false}
                      />
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="inline" className="space-y-3">
                  {recommendations.slice(0, 5).map((rec, index) => (
                    <div key={index} className="p-4 bg-muted/30 rounded-lg">
                      <AdMeshInlineRecommendation
                        recommendation={rec}
                        compact={false}
                        showReason={true}
                        onClick={(adId: string, admeshLink: string) => window.open(admeshLink, '_blank')}
                        variation="statement"
                      />
                    </div>
                  ))}
                </TabsContent>

                <TabsContent value="summary">
                  <AdMeshConversationSummary
                    recommendations={recommendations.slice(0, 3)}
                    summary={responseData?.response.summary || "Here are the top recommendations based on your query:"}
                    onRecommendationClick={(adId: string, admeshLink: string) => window.open(admeshLink, '_blank')}
                    showReasons={true}
                  />
                </TabsContent>
              </Tabs>

              {/* API Response Details */}
              {responseData && (
                <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">API Response Details</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(responseData, null, 2));
                        toast.success("API response copied to clipboard");
                      }}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy JSON
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Session ID:</span>
                      <p className="font-mono text-xs">{responseData.session_id}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Model Used:</span>
                      <p className="font-mono text-xs">{responseData.model_used}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Tokens Used:</span>
                      <p className="font-mono text-xs">{responseData.tokens_used}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Recommendation ID:</span>
                      <p className="font-mono text-xs">{responseData.recommendation_id}</p>
                    </div>
                  </div>
                  {responseData.response.summary && (
                    <div className="mt-4">
                      <span className="text-muted-foreground">AI Summary:</span>
                      <p className="text-sm mt-1 p-3 bg-background rounded border">{responseData.response.summary}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Explore More Ad Formats Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-primary/20">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row items-start gap-4">
              <div className="p-3 bg-primary/10 rounded-lg flex-shrink-0">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
              <div className="space-y-3 w-full">
                <h3 className="text-xl font-semibold">Explore More Ad Formats</h3>
                <p className="text-muted-foreground">
                  Discover additional ad formats and customization options available in the AdMesh platform.
                  From floating widgets to custom layouts, we have the perfect format for your use case.
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-4">
                  <div className="p-3 bg-background/60 rounded-lg border">
                    <h4 className="font-medium text-sm">Floating Widgets</h4>
                    <p className="text-xs text-muted-foreground mt-1">Overlay recommendations that appear contextually</p>
                  </div>
                  <div className="p-3 bg-background/60 rounded-lg border">
                    <h4 className="font-medium text-sm">Sidebar Components</h4>
                    <p className="text-xs text-muted-foreground mt-1">Persistent recommendation panels</p>
                  </div>
                  <div className="p-3 bg-background/60 rounded-lg border">
                    <h4 className="font-medium text-sm">Custom Layouts</h4>
                    <p className="text-xs text-muted-foreground mt-1">Build your own with our flexible API</p>
                  </div>
                </div>
                <div className="pt-3">
                  <Button
                    onClick={() => window.open("https://docs.useadmesh.com", "_blank")}
                    className="w-full sm:w-auto"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    View Documentation
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Dashboard Footer */}
      <DashboardFooter />

    </div>
  );
}
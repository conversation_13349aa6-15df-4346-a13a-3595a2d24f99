"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import { toast } from "sonner";
import {
  PlayCircle,
  Package,
  FileText,
  Sparkles,
  TrendingUp,
  Send,
  Loader2,
  MessageCircle,
  Lightbulb,
  AlertCircle,
  Key,
  Zap,
  Copy
} from "lucide-react";

// Import existing components
import InteractiveAgentDemo from "@/components/InteractiveAgentDemo";
import AgentEarningsCalculator from "@/components/AgentEarningsCalculator";
import { config } from "@/config/environment";

// Import AdMesh UI SDK components
import {
  AdMeshProductCard,
  AdMeshConversationSummary,
  AdMeshInlineRecommendation,
  AdMeshCitationUnit,
  AdMeshSidebar
} from "admesh-ui-sdk";

interface ApiKey {
  id: string;
  key: string;
  type: string;
  created_at: number;
  last_used: number | null;
  is_active: boolean;
  name: string;
}

interface Recommendation {
  ad_id: string;
  admesh_link: string;
  product_id: string;
  reason: string;
  title: string;
  intent_match_score?: number;
  features?: string[];
  has_free_tier?: boolean;
  integrations?: string[];
  pricing?: string;
  redirect_url?: string;
  reviews_summary?: string;
  reward_note?: string | null;
  security?: string[];
  slug?: string;
  support?: string[];
  trial_days?: number;
  url?: string;
}

interface RecommendationResponse {
  session_id: string;
  intent: any;
  response: {
    summary: string;
    recommendations: Recommendation[];
    followup_suggestions: string[];
  };
  tokens_used: number;
  model_used: string;
  recommendation_id: string;
  end_of_session: boolean;
}

// Demo queries with pre-stored LLM responses for consistent demo experience
const DEMO_QUERIES = [
  {
    query: "Best CRM for remote teams",
    demoData: null // Will be populated with real API response and cached
  },
  {
    query: "Project management tools for startups",
    demoData: null
  },
  {
    query: "Email marketing platforms with automation",
    demoData: null
  },
  {
    query: "Video conferencing software for large teams",
    demoData: null
  },
  {
    query: "Cloud storage solutions for businesses",
    demoData: null
  }
];

// Local storage key for caching demo responses
const DEMO_CACHE_KEY = 'admesh_demo_responses';

export default function DemoPage() {
  const { user } = useAuth();
  const [testApiKey, setTestApiKey] = useState<string | null>(null);
  const [isLoadingApiKey, setIsLoadingApiKey] = useState(true);
  const [selectedQuery, setSelectedQuery] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [responseData, setResponseData] = useState<RecommendationResponse | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<string>("card");
  const [error, setError] = useState<string | null>(null);
  const [demoCache, setDemoCache] = useState<Record<string, RecommendationResponse>>({});

  // Load demo cache from localStorage
  useEffect(() => {
    const cached = localStorage.getItem(DEMO_CACHE_KEY);
    if (cached) {
      try {
        setDemoCache(JSON.parse(cached));
      } catch (err) {
        console.error("Failed to parse demo cache:", err);
      }
    }
  }, []);

  // Fetch agent's test API key on component mount
  useEffect(() => {
    fetchTestApiKey();
  }, [user]);

  // Save demo cache to localStorage whenever it changes
  const saveDemoCache = (cache: Record<string, RecommendationResponse>) => {
    setDemoCache(cache);
    localStorage.setItem(DEMO_CACHE_KEY, JSON.stringify(cache));
  };

  // Clear demo cache (for development/testing)
  const clearDemoCache = () => {
    setDemoCache({});
    localStorage.removeItem(DEMO_CACHE_KEY);
    setRecommendations([]);
    setResponseData(null);
    setSelectedQuery("");
    toast.success("Demo cache cleared");
  };

  const fetchTestApiKey = async () => {
    if (!user) return;

    try {
      setIsLoadingApiKey(true);
      setError(null);
      const token = await user.getIdToken();

      // First get the agent data to ensure we have the correct agent_id
      const agentResponse = await fetch(`${config.api.baseUrl}/agent/onboarding/data`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (!agentResponse.ok) {
        if (agentResponse.status === 404) {
          throw new Error("Agent profile not found. Please complete your agent onboarding first.");
        }
        throw new Error("Failed to fetch agent data");
      }

      // Now get the API keys
      const response = await fetch(`${config.api.baseUrl}/api-keys/list`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error("You don't have permission to access API keys");
        }
        throw new Error(`Failed to fetch API keys (${response.status})`);
      }

      const data = await response.json();

      // Filter active keys by type - the API returns all keys in a 'keys' array
      const activeKeys = data.keys?.filter((key: ApiKey) => key.is_active) || [];
      const testKey = activeKeys.find((key: ApiKey) => key.type === "test");

      if (testKey) {
        setTestApiKey(testKey.key);
        setError(null);
      } else {
        setError("No active test API key found. Please generate a test API key to use the demo.");
      }
    } catch (err) {
      console.error("Error fetching test API key:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to load test API key. Please check your API keys page.";
      setError(errorMessage);
    } finally {
      setIsLoadingApiKey(false);
    }
  };

  const createTestApiKey = async () => {
    if (!user) return;

    try {
      setIsLoadingApiKey(true);
      setError(null);
      const token = await user.getIdToken();

      const response = await fetch(`${config.api.baseUrl}/api-keys/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          type: "test",
          name: "Test API Key for Demo"
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to create test API key");
      }

      const keyData = await response.json();
      setTestApiKey(keyData.key);
      toast.success("Test API key created successfully! You can now use the demo.");
    } catch (err) {
      console.error("Error creating test API key:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create test API key";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoadingApiKey(false);
    }
  };

  const callRecommendationAPI = async (query: string) => {
    if (!testApiKey) {
      toast.error("No test API key available");
      return;
    }

    setIsLoading(true);
    setError(null);
    setRecommendations([]);
    setResponseData(null);

    try {
      // Check if we have cached data for this query
      if (demoCache[query]) {
        const cachedData = demoCache[query];
        setResponseData(cachedData);
        setRecommendations(cachedData.response.recommendations || []);
        toast.success(`Loaded ${cachedData.response.recommendations.length} cached recommendations`);
        setIsLoading(false);
        return;
      }

      // Make API call for new data
      const response = await fetch(`${config.api.baseUrl}/agent/recommend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${testApiKey}`
        },
        body: JSON.stringify({
          query: query,
          format: "auto"
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `API request failed with status ${response.status}`);
      }

      const data: RecommendationResponse = await response.json();
      setResponseData(data);
      setRecommendations(data.response.recommendations || []);

      // Cache the response for future use
      const newCache = { ...demoCache, [query]: data };
      saveDemoCache(newCache);

      if (data.response.recommendations.length === 0) {
        toast.info("No recommendations found for this query");
      } else {
        toast.success(`Found ${data.response.recommendations.length} recommendations (cached for future use)`);
      }
    } catch (err) {
      console.error("Error calling recommendation API:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to get recommendations";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestedQueryClick = (query: string) => {
    setSelectedQuery(query);
    callRecommendationAPI(query);
  };

  return (
    <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-3 sm:space-y-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
          <div className="flex items-center gap-2">
            <PlayCircle className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            <h1 className="text-2xl sm:text-3xl font-bold">Try AdMesh Demo</h1>
          </div>
          <Badge variant="secondary" className="sm:ml-2">
            <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            Interactive
          </Badge>
        </div>
        <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto px-4 sm:px-0">
          Test the AdMesh recommendation API with your test API key and see live results in different ad formats.
        </p>
      </div>

      {/* API Key Status */}
      {isLoadingApiKey ? (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading test API key...</span>
            </div>
          </CardContent>
        </Card>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <span>{error}</span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchTestApiKey}
                  disabled={isLoadingApiKey}
                >
                  {isLoadingApiKey ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    "Retry"
                  )}
                </Button>
                {error?.includes("No active test API key found") && (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={createTestApiKey}
                    disabled={isLoadingApiKey}
                  >
                    {isLoadingApiKey ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <>
                        <Key className="h-4 w-4 mr-2" />
                        Create Test API Key
                      </>
                    )}
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open("/dashboard/agent/api-keys", "_blank")}
                >
                  <Key className="h-4 w-4 mr-2" />
                  Manage API Keys
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      ) : (
        <>
          {/* API Key Status Success */}
          <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
            <Key className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800 dark:text-green-200">
              Test API key loaded successfully. You can now test the recommendation API.
            </AlertDescription>
          </Alert>

          {/* Query Input Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  Test Recommendation API
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Select a query to test the AdMesh recommendation API with your test API key. Results are cached for faster subsequent loads.
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Demo Queries */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    <label className="text-sm font-medium">Demo Queries</label>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    {DEMO_QUERIES.map((demoQuery, index) => (
                      <Button
                        key={index}
                        variant={selectedQuery === demoQuery.query ? "default" : "outline"}
                        size="lg"
                        onClick={() => handleSuggestedQueryClick(demoQuery.query)}
                        disabled={isLoading}
                        className="text-left justify-between h-auto py-4 px-4"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium">
                            {index + 1}
                          </div>
                          <span className="font-medium">{demoQuery.query}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {demoCache[demoQuery.query] && (
                            <Badge variant="secondary" className="text-xs">
                              ✓ Cached
                            </Badge>
                          )}
                          {isLoading && selectedQuery === demoQuery.query ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Send className="h-4 w-4 opacity-60" />
                          )}
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Cache Management */}
                {Object.keys(demoCache).length > 0 && (
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        {Object.keys(demoCache).length} queries cached locally
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearDemoCache}
                        className="text-xs"
                      >
                        Clear Cache
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </>
      )}

      {/* Results Display Section */}
      {recommendations.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                API Results ({recommendations.length} recommendations)
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Live results from your test API key. Choose different ad formats to see how recommendations appear.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Format Selector */}
              <Tabs value={selectedFormat} onValueChange={setSelectedFormat}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="card">Product Card</TabsTrigger>
                  <TabsTrigger value="inline">Inline</TabsTrigger>
                  <TabsTrigger value="citation">Citation</TabsTrigger>
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                </TabsList>

                <TabsContent value="card" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {recommendations.slice(0, 6).map((rec, index) => (
                      <AdMeshProductCard
                        key={index}
                        recommendation={rec}
                        onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                        showFeatures={true}
                        showPricing={true}
                        compact={false}
                      />
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="inline" className="space-y-3">
                  {recommendations.slice(0, 5).map((rec, index) => (
                    <div key={index} className="p-4 bg-muted/30 rounded-lg">
                      <AdMeshInlineRecommendation
                        recommendation={rec}
                        compact={false}
                        showReason={true}
                        onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                        variation="statement"
                      />
                    </div>
                  ))}
                </TabsContent>

                <TabsContent value="citation" className="space-y-3">
                  {recommendations.slice(0, 5).map((rec, index) => (
                    <AdMeshCitationUnit
                      key={index}
                      recommendation={rec}
                      citationNumber={index + 1}
                      onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                      showReason={true}
                    />
                  ))}
                </TabsContent>

                <TabsContent value="summary">
                  <AdMeshConversationSummary
                    recommendations={recommendations.slice(0, 3)}
                    summary={responseData?.response.summary || "Here are the top recommendations based on your query:"}
                    onRecommendationClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                    showReasons={true}
                  />
                </TabsContent>
              </Tabs>

              {/* API Response Details */}
              {responseData && (
                <div className="mt-6 p-4 bg-muted/30 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">API Response Details</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(responseData, null, 2));
                        toast.success("API response copied to clipboard");
                      }}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy JSON
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Session ID:</span>
                      <p className="font-mono text-xs">{responseData.session_id}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Model Used:</span>
                      <p className="font-mono text-xs">{responseData.model_used}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Tokens Used:</span>
                      <p className="font-mono text-xs">{responseData.tokens_used}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Recommendation ID:</span>
                      <p className="font-mono text-xs">{responseData.recommendation_id}</p>
                    </div>
                  </div>
                  {responseData.response.summary && (
                    <div className="mt-4">
                      <span className="text-muted-foreground">AI Summary:</span>
                      <p className="text-sm mt-1 p-3 bg-background rounded border">{responseData.response.summary}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Interactive Demo Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card>
          <CardHeader className="pb-4 sm:pb-6">
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Package className="h-4 w-4 sm:h-5 sm:w-5" />
              Live Ad Unit Components
            </CardTitle>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Try different ad unit types with real AdMesh SDK components. All components are interactive and use live demo data.
            </p>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <InteractiveAgentDemo />
          </CardContent>
        </Card>
      </motion.div>

      {/* Earnings Calculator Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardHeader className="pb-4 sm:pb-6">
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5" />
              Revenue Calculator
            </CardTitle>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Calculate your potential earnings based on your platform&apos;s traffic and user engagement.
            </p>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <AgentEarningsCalculator hideCTA={true} />
          </CardContent>
        </Card>
      </motion.div>

      {/* Developer Freedom Notice */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-primary/20">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
              <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0">
                <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              </div>
              <div className="space-y-2 w-full">
                <h3 className="text-base sm:text-lg font-semibold">Complete Developer Freedom</h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  You&apos;re not limited to our UI components. AdMesh provides:
                </p>
                <ul className="space-y-1 text-xs sm:text-sm text-muted-foreground">
                  <li>• <strong>Pre-built Components:</strong> Ready-to-use React components for quick integration</li>
                  <li>• <strong>Data API:</strong> Raw recommendation data to build your own custom UI</li>
                  <li>• <strong>Flexible Styling:</strong> Customize our components or create entirely new designs</li>
                  <li>• <strong>Multiple Formats:</strong> Choose from inline, cards, citations, summaries, and more</li>
                </ul>
                <div className="pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto"
                    onClick={() => window.open("https://docs.useadmesh.com/", "_blank")}
                  >
                    View API Documentation
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      
    </div>
  );
}
"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Code, Lightbulb, ExternalLink, Copy, Check, ChevronRight } from "lucide-react";
import { toast } from "sonner";
// import { cn } from "@/lib/utils";

export default function AgentDocsPage() {
  const [activeTab, setActiveTab] = useState("introduction");
  const [activeSubTab, setActiveSubTab] = useState<string | null>(null);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const copyToClipboard = (text: string, id: string = "default") => {
    navigator.clipboard.writeText(text);
    setCopiedId(id);
    toast.success("Code copied to clipboard");
    setTimeout(() => setCopiedId(null), 2000);
  };

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Documentation</h1>
        <div className="flex space-x-2">
          <Button asChild variant="outline">
            <Link href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" className="flex items-center">
              <Code className="mr-2 h-4 w-4" /> TypeScript SDK
              <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" className="flex items-center">
              <Code className="mr-2 h-4 w-4" /> Python SDK
              <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="docs" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="docs">Documentation</TabsTrigger>
        </TabsList>
        <TabsContent value="docs" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="md:col-span-1">
              <div className="sticky top-8">
                <nav className="space-y-1">
                  <div className="mb-2">
                    <Button
                      variant={activeTab === "introduction" ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveTab("introduction");
                        setActiveSubTab(null);
                      }}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      Introduction
                    </Button>
                  </div>

                  <div className="mb-2">
                    <Button
                      variant={activeTab === "how-it-works" ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveTab("how-it-works");
                        setActiveSubTab(null);
                      }}
                    >
                      <Lightbulb className="mr-2 h-4 w-4" />
                      How It Works
                    </Button>
                  </div>

                  <div className="mb-2">
                    <Button
                      variant={activeTab === "integration" ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveTab("integration");
                        setActiveSubTab(null);
                      }}
                    >
                      <Code className="mr-2 h-4 w-4" />
                      Integration
                    </Button>

                    {activeTab === "integration" && (
                      <div className="ml-6 mt-2 space-y-1 border-l-2 border-muted pl-2">
                        <Button
                          variant={activeSubTab === "prerequisites" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("prerequisites")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Prerequisites
                        </Button>
                        <Button
                          variant={activeSubTab === "sdk-install" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("sdk-install")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Install SDK
                        </Button>
                        <Button
                          variant={activeSubTab === "sdk-init" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("sdk-init")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Initialize SDK
                        </Button>
                        <Button
                          variant={activeSubTab === "get-recommendations" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("get-recommendations")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Get Recommendations
                        </Button>
                        <Button
                          variant={activeSubTab === "direct-api" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("direct-api")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Direct API
                        </Button>
                        <Button
                          variant={activeSubTab === "testing" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("testing")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Testing
                        </Button>
                        <Button
                          variant={activeSubTab === "going-live" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("going-live")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          Going Live
                        </Button>
                        <Button
                          variant={activeSubTab === "best-practices" ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm"
                          onClick={() => setActiveSubTab("best-practices")}
                        >
                          <ChevronRight className="mr-1 h-3 w-3" />
                          UI/UX Best Practices
                        </Button>
                      </div>
                    )}
                  </div>
                </nav>
              </div>
            </div>

            {/* Content */}
            <div className="md:col-span-3">
              <Card className="border-none shadow-none">
                <CardContent className="p-0">
              {activeTab === "introduction" && (
                <div className="prose dark:prose-invert max-w-none">
                  <h2 className="text-3xl font-bold mb-6">Introduction to AdMesh</h2>
                  <p className="text-lg mb-4">
                    AdMesh is a platform that connects AI agents with brands and products, enabling agents to provide relevant product recommendations to users while earning revenue through conversions.
                  </p>
                  <h3 className="text-xl font-semibold mt-8 mb-4">What is AdMesh?</h3>
                  <p className="mb-4">
                    AdMesh is a recommendation network designed specifically for AI agents. It allows your agent to recommend relevant products to users based on their queries, and earn revenue when those recommendations lead to conversions.
                  </p>
                  <p className="mb-4">
                    Our platform connects AI agents with brands and products across various categories, ensuring that recommendations are relevant, helpful, and valuable to users.
                  </p>
                  <h3 className="text-xl font-semibold mt-8 mb-4">Why Use AdMesh?</h3>
                  <ul className="list-disc pl-6 mb-6 space-y-2">
                    <li><strong>Monetize Your Agent:</strong> Earn revenue through product recommendations and conversions.</li>
                    <li><strong>Enhance User Experience:</strong> Provide valuable product recommendations that help users solve their problems.</li>
                    <li><strong>Easy Integration:</strong> Simple API integration with your existing agent.</li>
                    <li><strong>Transparent Analytics:</strong> Track performance, conversions, and earnings in real-time.</li>
                    <li><strong>Curated Products:</strong> Access to a growing network of quality brands and products.</li>
                  </ul>
                  <h3 className="text-xl font-semibold mt-8 mb-4">Getting Started</h3>
                  <p className="mb-4">
                    To get started with AdMesh, you&apos;ll need to:
                  </p>
                  <ol className="list-decimal pl-6 mb-6 space-y-2">
                    <li>Sign up for an AdMesh agent account</li>
                    <li>Complete the onboarding process</li>
                    <li>Integrate the AdMesh API with your agent</li>
                    <li>Start recommending products and earning revenue</li>
                  </ol>
                  <p className="mb-4">
                    The following sections will guide you through the process of understanding how AdMesh works and how to integrate it with your agent.
                  </p>
                </div>
              )}

              {activeTab === "how-it-works" && (
                <div className="prose dark:prose-invert max-w-none">
                  <h2 className="text-3xl font-bold mb-6">How AdMesh Works for Agents</h2>
                  <p className="text-lg mb-4">
                    AdMesh connects your AI agent with relevant products and brands, enabling you to provide valuable recommendations to your users and earn revenue through conversions.
                  </p>

                  <h3 className="text-xl font-semibold mt-8 mb-4">The AdMesh Workflow</h3>
                  <ol className="list-decimal pl-6 mb-6 space-y-4">
                    <li>
                      <strong>User Query:</strong> A user asks your agent a question that might benefit from product recommendations.
                    </li>
                    <li>
                      <strong>API Request:</strong> Your agent sends a request to the AdMesh API with the user&apos;s query and context.
                    </li>
                    <li>
                      <strong>Recommendation Generation:</strong> AdMesh analyzes the query and returns relevant product recommendations.
                    </li>
                    <li>
                      <strong>Presentation:</strong> Your agent presents these recommendations to the user in a helpful, contextual way.
                    </li>
                    <li>
                      <strong>Tracking:</strong> If the user clicks on a recommendation, AdMesh tracks this interaction.
                    </li>
                    <li>
                      <strong>Conversion:</strong> If the user makes a purchase, you earn a commission on the sale.
                    </li>
                  </ol>

                  <h3 className="text-xl font-semibold mt-8 mb-4">Key Components</h3>

                  <h4 className="text-lg font-semibold mt-6 mb-2">1. API Keys</h4>
                  <p className="mb-4">
                    AdMesh provides two types of API keys:
                  </p>
                  <ul className="list-disc pl-6 mb-4">
                    <li><strong>Test Key:</strong> For development and testing. No real conversions or earnings.</li>
                    <li><strong>Production Key:</strong> For live environments. Real conversions and earnings.</li>
                  </ul>

                  <h4 className="text-lg font-semibold mt-6 mb-2">2. Recommendation API</h4>
                  <p className="mb-4">
                    The core of AdMesh is the recommendation API, which:
                  </p>
                  <ul className="list-disc pl-6 mb-4">
                    <li>Accepts user queries and context</li>
                    <li>Returns relevant product recommendations</li>
                    <li>Provides tracking links for each recommendation</li>
                  </ul>

                  <h4 className="text-lg font-semibold mt-6 mb-2">3. Tracking System</h4>
                  <p className="mb-4">
                    AdMesh&apos;s tracking system:
                  </p>
                  <ul className="list-disc pl-6 mb-4">
                    <li>Monitors clicks on recommendation links</li>
                    <li>Tracks conversions (purchases)</li>
                    <li>Attributes conversions to the correct agent</li>
                  </ul>

                  <h4 className="text-lg font-semibold mt-6 mb-2">4. Analytics Dashboard</h4>
                  <p className="mb-4">
                    The AdMesh dashboard provides:
                  </p>
                  <ul className="list-disc pl-6 mb-4">
                    <li>Real-time performance metrics</li>
                    <li>Conversion tracking</li>
                    <li>Earnings reports</li>
                    <li>Recommendation quality insights</li>
                  </ul>

                  <h3 className="text-xl font-semibold mt-8 mb-4">Best Practices</h3>
                  <ul className="list-disc pl-6 mb-6 space-y-2">
                    <li><strong>Contextual Recommendations:</strong> Only recommend products when they&apos;re genuinely helpful to the user.</li>
                    <li><strong>Transparency:</strong> Be clear when you&apos;re making sponsored recommendations.</li>
                    <li><strong>Quality Over Quantity:</strong> Focus on making fewer, more relevant recommendations rather than many irrelevant ones.</li>
                    <li><strong>User Experience:</strong> Integrate recommendations seamlessly into your agent&apos;s responses.</li>
                  </ul>
                </div>
              )}

              {activeTab === "integration" && (
                <div className="prose dark:prose-invert max-w-none">
                  <h2 className="text-3xl font-bold mb-6">Integrating AdMesh with Your Agent</h2>
                  <p className="text-lg mb-4">
                    Follow these steps to integrate AdMesh with your AI agent and start providing product recommendations.
                  </p>

                  {(!activeSubTab || activeSubTab === "prerequisites") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="prerequisites">Prerequisites</h3>
                      <ul className="list-disc pl-6 mb-6 space-y-2">
                        <li>An AdMesh agent account with completed onboarding</li>
                        <li>API keys (available in your <Link href="/dashboard/agent/api-keys" className="text-primary hover:underline">API Keys</Link> section)</li>
                        <li>Your agent&apos;s implementation (API, chatbot, etc.)</li>
                      </ul>
                    </>
                  )}

                  {(!activeSubTab || activeSubTab === "sdk-install") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="sdk-install">1. Install the SDK (Optional)</h3>
                      <p className="mb-4">
                        We provide SDKs for Python and TypeScript to simplify integration. Both SDKs are open source and available on GitHub:
                      </p>
                      <ul className="list-disc pl-6 mb-4">
                        <li><a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">admesh-python on GitHub</a> - The official Python library for the AdMesh API</li>
                        <li><a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">admesh-typescript on GitHub</a> - The official TypeScript library for the AdMesh API</li>
                      </ul>
                      <p className="mb-4">
                        Install the SDK for your preferred language:
                      </p>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>pip install admesh-python</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard("pip install admesh-python", "pip")}
                        >
                          {copiedId === "pip" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>npm install admesh</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard("npm install admesh", "npm")}
                        >
                          {copiedId === "npm" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    </>
                  )}

                  {(!activeSubTab || activeSubTab === "sdk-init") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="sdk-init">2. Initialize the SDK</h3>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>{`# Python
import admesh

client = admesh.Admesh(api_key="your_api_key")
`}</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`# Python
import admesh

client = admesh.Admesh(api_key="your_api_key")`, "python-init")}
                        >
                          {copiedId === "python-init" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>{`// TypeScript
import Admesh from 'admesh';

const client = new Admesh({
  apiKey: 'your_api_key'
});`}</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`// TypeScript
import Admesh from 'admesh';

const client = new Admesh({
  apiKey: 'your_api_key'
});`, "ts-init")}
                        >
                          {copiedId === "ts-init" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    </>
                  )}

                  {(!activeSubTab || activeSubTab === "get-recommendations") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="get-recommendations">3. Get Recommendations</h3>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>{`# Python
response = client.recommend.get_recommendations(
    query="Best AI tools for content creation",
    format="auto"
)

for rec in response.response.recommendations:
    print(f"Product: {rec.title}")
    print(f"Reason: {rec.reason}")
    print(f"Link: {rec.admesh_link}")
`}</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`# Python
response = client.recommend.get_recommendations(
    query="Best AI tools for content creation",
    format="auto"
)

for rec in response.response.recommendations:
    print(f"Product: {rec.title}")
    print(f"Reason: {rec.reason}")
    print(f"Link: {rec.admesh_link}")
`, "python-rec")}
                        >
                          {copiedId === "python-rec" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>{`// TypeScript
const response = await client.recommend.getRecommendations({
  query: "Best AI tools for content creation",
  format: "auto"
});

response.response?.recommendations?.forEach(rec => {
  console.log(\`Product: \${rec.title}\`);
  console.log(\`Reason: \${rec.reason}\`);
  console.log(\`Link: \${rec.admesh_link}\`);
});`}</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`// TypeScript
const response = await client.recommend.getRecommendations({
  query: "Best AI tools for content creation",
  format: "auto"
});

response.response?.recommendations?.forEach(rec => {
  console.log(\`Product: \${rec.title}\`);
  console.log(\`Reason: \${rec.reason}\`);
  console.log(\`Link: \${rec.admesh_link}\`);
});`, "ts-rec")}
                        >
                          {copiedId === "ts-rec" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    </>
                  )}

                  {(!activeSubTab || activeSubTab === "direct-api") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="direct-api">4. Direct API Integration</h3>
                      <p className="mb-4">
                        If you prefer to use the API directly:
                      </p>

                      <div className="bg-muted rounded-md p-4 mb-4 relative">
                        <pre className="text-sm overflow-x-auto"><code>{`curl -X POST https://api.useadmesh.com/agent/recommend \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your_api_key" \\
  -d '{
    "query": "Best AI tools for content creation",
    "format": "auto"
  }'`}</code></pre>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`curl -X POST https://api.useadmesh.com/agent/recommend \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your_api_key" \\
  -d '{
    "query": "Best AI tools for content creation",
    "format": "auto"
  }'`, "curl")}
                        >
                          {copiedId === "curl" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    </>
                  )}

                  {(!activeSubTab || activeSubTab === "testing") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="testing">Testing Your Integration</h3>
                      <p className="mb-4">
                        To test your integration:
                      </p>
                      <ol className="list-decimal pl-6 mb-6 space-y-2">
                        <li>Use your test API key for development</li>
                        <li>Make recommendation requests with various queries</li>
                        <li>Click on the tracking links to simulate user interactions</li>
                        <li>Check your dashboard to verify that clicks are being tracked</li>
                      </ol>
                    </>
                  )}

                  {(!activeSubTab || activeSubTab === "going-live") && (
                    <>
                      <h3 className="text-xl font-semibold mt-8 mb-4" id="going-live">Going Live</h3>
                      <p className="mb-4">
                        When you&apos;re ready to go live:
                      </p>
                      <ol className="list-decimal pl-6 mb-6 space-y-2">
                        <li>Switch to your production API key</li>
                        <li>Ensure your agent is presenting recommendations appropriately</li>
                        <li>Monitor your dashboard for performance metrics</li>
                      </ol>

                      <h3 className="text-xl font-semibold mt-8 mb-4">Support</h3>
                      <p className="mb-4">
                        If you need help with integration or have questions about best practices, please contact our support team at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>.
                      </p>
                    </>
                  )}

                  {activeSubTab === "best-practices" && (
                    <>
                      <h3 className="text-xl font-semibold mb-6" id="best-practices">Best Practices for Agent UI/UX</h3>
                      <p className="mb-4">
                        Integrating product recommendations into your agent&apos;s responses requires careful consideration of user experience. Here are some best practices:
                      </p>

                      <h4 className="text-lg font-semibold mt-6 mb-2">1. Contextual Recommendations</h4>
                      <p className="mb-4">
                        Only provide recommendations when they&apos;re genuinely relevant to the user&apos;s query. Irrelevant recommendations can damage trust in your agent.
                      </p>
                      <div className="bg-muted rounded-md p-4 mb-4">
                        <p className="font-medium">Example:</p>
                        <p className="text-sm mt-2"><strong>User:</strong> &quot;What are some good tools for video editing?&quot;</p>
                        <p className="text-sm mt-1"><strong>Agent:</strong> &quot;For video editing, I&apos;d recommend considering tools like Adobe Premiere Pro for professional editing, DaVinci Resolve for color grading, or Filmora for beginners. Based on your needs, [Product Name] offers an excellent balance of features and ease of use. It includes [key features] and is popular among [target audience]. You can check it out here: [tracking link]&quot;</p>
                      </div>

                      <h4 className="text-lg font-semibold mt-6 mb-2">2. Transparent Disclosure</h4>
                      <p className="mb-4">
                        Always be transparent about sponsored or monetized recommendations. This builds trust with users and complies with advertising regulations.
                      </p>
                      <div className="bg-muted rounded-md p-4 mb-4">
                        <p className="font-medium">Example disclosure:</p>
                        <p className="text-sm mt-2">&quot;Here are some recommended products that might help (affiliate links):&quot;</p>
                        <p className="text-sm mt-2">&quot;I can suggest some tools that might be useful (I may earn a commission if you purchase through these links):&quot;</p>
                      </div>

                      <h4 className="text-lg font-semibold mt-6 mb-2">3. Natural Integration</h4>
                      <p className="mb-4">
                        Integrate recommendations naturally into your agent&apos;s responses rather than making them feel like intrusive advertisements.
                      </p>
                      <div className="bg-muted rounded-md p-4 mb-4">
                        <p className="font-medium">Instead of:</p>
                        <p className="text-sm mt-2">&quot;BUY THIS PRODUCT NOW! CLICK HERE!&quot;</p>
                        <p className="font-medium mt-3">Try:</p>
                        <p className="text-sm mt-2">&quot;Based on your requirements, you might find [Product Name] helpful. It offers [specific features that address the user&apos;s needs] and has received positive reviews for [relevant benefit].&quot;</p>
                      </div>

                      <h4 className="text-lg font-semibold mt-6 mb-2">4. Provide Value First</h4>
                      <p className="mb-4">
                        Always provide valuable information before making recommendations. This establishes your agent as a helpful resource rather than just a sales channel.
                      </p>
                      <div className="bg-muted rounded-md p-4 mb-4">
                        <p className="font-medium">Example structure:</p>
                        <ol className="list-decimal pl-6 text-sm mt-2">
                          <li>Answer the user&apos;s question thoroughly</li>
                          <li>Provide general advice or information</li>
                          <li>Then introduce relevant product recommendations</li>
                        </ol>
                      </div>

                      <h4 className="text-lg font-semibold mt-6 mb-2">5. Format for Readability</h4>
                      <p className="mb-4">
                        Format your recommendations in a way that&apos;s easy to read and understand. Use clear headings, bullet points, and concise descriptions.
                      </p>
                      <div className="bg-muted rounded-md p-4 mb-4">
                        <p className="font-medium">Example format:</p>
                        <div className="text-sm mt-2">
                          <p><strong>Recommended Tool: [Product Name]</strong></p>
                          <ul className="list-disc pl-6 mt-1">
                            <li>Best for: [specific use case]</li>
                            <li>Key features: [feature 1], [feature 2], [feature 3]</li>
                            <li>Price: [price or pricing model]</li>
                            <li><a href="#" className="text-primary">Learn more</a> (affiliate link)</li>
                          </ul>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
